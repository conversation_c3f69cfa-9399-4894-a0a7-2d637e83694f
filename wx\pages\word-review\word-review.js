/**
 * 字词复习页面
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    reviewType: 'today', // today: 今日复习, weak: 薄弱字词复习, date: 指定日期复习
    reviewDate: '', // 指定复习日期
    currentIndex: 0,
    totalCount: 0,
    words: [],
    currentWord: null,
    progress: 0,
    showCompleteModal: false,
    reviewResults: {
      total: 0,
      mastered: 0,
      needReview: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const reviewType = options.type || 'today';
    const reviewDate = options.date || '';

    this.setData({
      reviewType,
      reviewDate
    });

    this.loadReviewWords();
  },

  /**
   * 加载复习字词
   */
  loadReviewWords: function() {
    const currentChild = app.getCurrentChild();
    if (!currentChild) {
      wx.showToast({
        title: '请先设置幼儿信息',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    let words = [];

    if (this.data.reviewType === 'today') {
      // 获取今日复习任务
      const todayTasks = app.ebbinghausManager.getTodayReviewTasks(currentChild.child_id);
      words = todayTasks.map(task => ({
        ...task.word_info,
        planId: task.plan_id,
        reviewStage: task.review_stage
      }));

      // 添加今日新字词
      const allWords = app.dataManager.getWordLibrary(currentChild.child_id);
      const today = app.dataManager.getCurrentDate();
      const todayNewWords = allWords.filter(word =>
        word.create_time && word.create_time.startsWith(today) &&
        !words.find(w => w.word_id === word.word_id)
      );

      words.push(...todayNewWords.map(word => ({
        ...word,
        isNewWord: true
      })));
    } else if (this.data.reviewType === 'weak') {
      // 薄弱字词复习模式
      const weakWords = app.ebbinghausManager.getWeakWords(currentChild.child_id);
      words = weakWords.map(word => ({
        ...word,
        isWeakWord: true
      }));
    } else if (this.data.reviewType === 'date') {
      // 指定日期复习模式
      const allPlans = app.dataManager.getReviewPlans(currentChild.child_id);
      const dateTasks = allPlans.filter(plan => plan.review_date === this.data.reviewDate);

      const wordLibrary = app.dataManager.getWordLibrary(currentChild.child_id);
      words = dateTasks.map(task => {
        const word = wordLibrary.find(w => w.word_id === task.word_id);
        return {
          ...word,
          planId: task.plan_id,
          reviewStage: task.review_stage,
          isDateReview: true
        };
      }).filter(word => word.word_id); // 过滤掉已删除的字词
    } else {
      // 历史复习模式
      words = app.dataManager.getWordLibrary(currentChild.child_id);
    }

    if (words.length === 0) {
      wx.showModal({
        title: '暂无复习内容',
        content: '当前没有需要复习的字词',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({
      words,
      totalCount: words.length,
      currentWord: words[0],
      reviewResults: {
        total: words.length,
        mastered: 0,
        needReview: 0
      }
    });

    this.updateProgress();
  },

  /**
   * 更新进度
   */
  updateProgress: function() {
    const progress = this.data.totalCount > 0 ? 
      Math.round((this.data.currentIndex / this.data.totalCount) * 100) : 0;
    
    this.setData({
      progress
    });
  },

  /**
   * 标记已掌握
   */
  onMastered: function() {
    this.markWordStatus(true);
  },

  /**
   * 标记需复习
   */
  onNeedReview: function() {
    this.markWordStatus(false);
  },

  /**
   * 标记字词状态
   */
  markWordStatus: function(mastered) {
    const currentWord = this.data.currentWord;
    const currentChild = app.getCurrentChild();
    
    // 更新字词状态
    app.dataManager.updateWordStatus(currentWord.word_id, mastered);
    
    // 如果是复习计划中的字词，标记计划完成
    if (currentWord.planId) {
      app.ebbinghausManager.markTaskCompleted(currentWord.planId, mastered);
    }
    
    // 如果是新字词，创建复习计划
    if (currentWord.isNewWord) {
      app.ebbinghausManager.createReviewPlansForWords(
        currentChild.child_id, 
        [currentWord.word_id]
      );
    }

    // 更新统计
    const results = this.data.reviewResults;
    if (mastered) {
      results.mastered++;
    } else {
      results.needReview++;
    }
    
    this.setData({
      reviewResults: results
    });

    // 下一个字词
    this.nextWord();
  },

  /**
   * 下一个字词
   */
  nextWord: function() {
    const nextIndex = this.data.currentIndex + 1;
    
    if (nextIndex >= this.data.totalCount) {
      // 复习完成
      this.completeReview();
    } else {
      this.setData({
        currentIndex: nextIndex,
        currentWord: this.data.words[nextIndex]
      });
      this.updateProgress();
    }
  },

  /**
   * 上一个字词
   */
  prevWord: function() {
    if (this.data.currentIndex > 0) {
      const prevIndex = this.data.currentIndex - 1;
      this.setData({
        currentIndex: prevIndex,
        currentWord: this.data.words[prevIndex]
      });
      this.updateProgress();
    }
  },

  /**
   * 完成复习
   */
  completeReview: function() {
    const currentChild = app.getCurrentChild();
    const results = this.data.reviewResults;
    
    // 保存学习记录
    const weakWords = this.data.words
      .filter((word, index) => index < this.data.currentIndex && !word.mastered_status)
      .map(word => word.word_text);
    
    app.dataManager.saveLearningRecord(
      currentChild.child_id,
      results.total,
      results.mastered,
      weakWords
    );

    // 显示完成弹窗
    this.setData({
      showCompleteModal: true
    });
  },

  /**
   * 关闭完成弹窗
   */
  closeCompleteModal: function() {
    this.setData({
      showCompleteModal: false
    });
    wx.navigateBack();
  },

  /**
   * 返回首页
   */
  goHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  /**
   * 跳转到指定字词
   */
  goToWord: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentIndex: index,
      currentWord: this.data.words[index]
    });
    this.updateProgress();
  }
});
