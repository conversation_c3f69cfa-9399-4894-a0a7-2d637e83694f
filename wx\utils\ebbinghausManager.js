/**
 * 艾宾浩斯遗忘曲线管理器
 * 负责复习计划的生成和管理
 */

const DataManager = require('./dataManager.js');

class EbbinghausManager {
  constructor() {
    this.dataManager = new DataManager();
    
    // 艾宾浩斯遗忘曲线复习间隔（天数）
    this.reviewIntervals = [1, 2, 4, 7, 15];
  }

  /**
   * 获取指定日期后N天的日期
   */
  getDateAfterDays(dateStr, days) {
    const date = new Date(dateStr);
    date.setDate(date.getDate() + days);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }

  /**
   * 为新学字词创建复习计划
   */
  createReviewPlansForWords(childId, wordIds) {
    const userInfo = this.dataManager.getUserInfo();
    if (!userInfo) return false;

    const currentDate = this.dataManager.getCurrentDate();
    const existingPlans = this.dataManager.getReviewPlans();

    wordIds.forEach(wordId => {
      // 检查是否已存在该字词的复习计划
      const hasExistingPlan = existingPlans.some(plan =>
        plan.word_id === wordId &&
        plan.child_id === childId &&
        !plan.completed
      );

      // 如果已存在未完成的复习计划，跳过创建
      if (hasExistingPlan) {
        console.log(`字词 ${wordId} 已存在复习计划，跳过创建`);
        return;
      }

      // 为每个复习阶段创建计划
      this.reviewIntervals.forEach((interval, index) => {
        const reviewDate = this.getDateAfterDays(currentDate, interval);

        const plan = {
          plan_id: this.dataManager.generateUUID(),
          child_id: childId,
          user_id: userInfo.user_id,
          word_id: wordId,
          review_date: reviewDate,
          review_stage: index + 1,
          completed: false,
          create_time: this.dataManager.getCurrentDateTime()
        };

        existingPlans.push(plan);
      });
    });

    return this.dataManager.saveReviewPlans(existingPlans);
  }

  /**
   * 获取今日复习任务
   */
  getTodayReviewTasks(childId) {
    const currentDate = this.dataManager.getCurrentDate();
    const allPlans = this.dataManager.getReviewPlans(childId);
    
    // 筛选今日及之前未完成的复习任务
    const todayTasks = allPlans.filter(plan => 
      plan.review_date <= currentDate && !plan.completed
    );

    // 获取对应的字词信息
    const wordLibrary = this.dataManager.getWordLibrary(childId);
    const tasksWithWords = todayTasks.map(task => {
      const word = wordLibrary.find(w => w.word_id === task.word_id);
      return {
        ...task,
        word_info: word
      };
    }).filter(task => task.word_info); // 过滤掉已删除的字词

    return tasksWithWords;
  }

  /**
   * 标记复习任务完成
   */
  markTaskCompleted(planId, masteredStatus) {
    const allPlans = this.dataManager.getReviewPlans();
    const planIndex = allPlans.findIndex(plan => plan.plan_id === planId);
    
    if (planIndex !== -1) {
      allPlans[planIndex].completed = true;
      allPlans[planIndex].completed_time = this.dataManager.getCurrentDateTime();
      allPlans[planIndex].mastered_result = masteredStatus;
      
      // 如果未掌握，创建额外的复习计划
      if (!masteredStatus) {
        this.createAdditionalReviewPlan(allPlans[planIndex]);
      }
      
      return this.dataManager.saveReviewPlans(allPlans);
    }
    return false;
  }

  /**
   * 为未掌握的字词创建额外复习计划
   */
  createAdditionalReviewPlan(originalPlan) {
    const userInfo = this.dataManager.getUserInfo();
    if (!userInfo) return;

    const currentDate = this.dataManager.getCurrentDate();
    const nextReviewDate = this.getDateAfterDays(currentDate, 1); // 明天再复习

    const allPlans = this.dataManager.getReviewPlans();

    // 检查是否已存在相同的额外复习计划
    const hasExistingAdditionalPlan = allPlans.some(plan =>
      plan.word_id === originalPlan.word_id &&
      plan.child_id === originalPlan.child_id &&
      plan.review_date === nextReviewDate &&
      plan.review_stage === originalPlan.review_stage &&
      plan.is_additional === true &&
      !plan.completed
    );

    if (hasExistingAdditionalPlan) {
      console.log(`字词 ${originalPlan.word_id} 已存在额外复习计划，跳过创建`);
      return;
    }

    const additionalPlan = {
      plan_id: this.dataManager.generateUUID(),
      child_id: originalPlan.child_id,
      user_id: userInfo.user_id,
      word_id: originalPlan.word_id,
      review_date: nextReviewDate,
      review_stage: originalPlan.review_stage, // 保持同一阶段
      completed: false,
      is_additional: true, // 标记为额外复习
      create_time: this.dataManager.getCurrentDateTime()
    };

    allPlans.push(additionalPlan);
    this.dataManager.saveReviewPlans(allPlans);
  }

  /**
   * 获取复习日历数据
   */
  getReviewCalendar(childId, year, month) {
    const allPlans = this.dataManager.getReviewPlans(childId);
    const calendar = {};
    
    // 筛选指定月份的复习计划
    const monthPlans = allPlans.filter(plan => {
      const planDate = new Date(plan.review_date);
      return planDate.getFullYear() === year && planDate.getMonth() + 1 === month;
    });

    // 按日期分组
    monthPlans.forEach(plan => {
      const date = plan.review_date;
      if (!calendar[date]) {
        calendar[date] = {
          total: 0,
          completed: 0,
          stages: {}
        };
      }
      
      calendar[date].total++;
      if (plan.completed) {
        calendar[date].completed++;
      }
      
      // 统计各阶段数量
      const stage = plan.review_stage;
      if (!calendar[date].stages[stage]) {
        calendar[date].stages[stage] = 0;
      }
      calendar[date].stages[stage]++;
    });

    return calendar;
  }

  /**
   * 获取复习统计数据
   */
  getReviewStatistics(childId) {
    const allPlans = this.dataManager.getReviewPlans(childId);
    const wordLibrary = this.dataManager.getWordLibrary(childId);
    const currentDate = this.dataManager.getCurrentDate();
    
    // 总体统计
    const totalWords = wordLibrary.length;
    const masteredWords = wordLibrary.filter(word => word.mastered_status).length;
    
    // 今日任务统计
    const todayTasks = this.getTodayReviewTasks(childId);
    const todayTotal = todayTasks.length;
    const todayCompleted = allPlans.filter(plan => 
      plan.review_date === currentDate && plan.completed
    ).length;
    
    // 本周统计
    const weekStart = this.getWeekStartDate(currentDate);
    const weekEnd = this.getDateAfterDays(weekStart, 6);
    const weekTasks = allPlans.filter(plan => 
      plan.review_date >= weekStart && plan.review_date <= weekEnd
    );
    const weekCompleted = weekTasks.filter(plan => plan.completed).length;
    
    return {
      total_words: totalWords,
      mastered_words: masteredWords,
      today_total: todayTotal,
      today_completed: todayCompleted,
      week_total: weekTasks.length,
      week_completed: weekCompleted,
      mastery_rate: totalWords > 0 ? Math.round((masteredWords / totalWords) * 100) : 0
    };
  }

  /**
   * 获取一周开始日期（周一）
   */
  getWeekStartDate(dateStr) {
    const date = new Date(dateStr);
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
    date.setDate(diff);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }

  /**
   * 更新复习计划（清理过期数据等）
   */
  updateReviewPlans(childId) {
    const allPlans = this.dataManager.getReviewPlans();
    const currentDate = this.dataManager.getCurrentDate();
    
    // 清理30天前的已完成计划
    const cutoffDate = this.getDateAfterDays(currentDate, -30);
    const filteredPlans = allPlans.filter(plan => {
      if (plan.child_id !== childId) return true; // 保留其他幼儿的计划
      if (!plan.completed) return true; // 保留未完成的计划
      return plan.review_date >= cutoffDate; // 保留30天内的已完成计划
    });

    if (filteredPlans.length !== allPlans.length) {
      this.dataManager.saveReviewPlans(filteredPlans);
    }
  }

  /**
   * 获取薄弱字词（多次复习仍未掌握）
   */
  getWeakWords(childId) {
    const wordLibrary = this.dataManager.getWordLibrary(childId);
    const allPlans = this.dataManager.getReviewPlans(childId);
    
    const weakWords = wordLibrary.filter(word => {
      if (word.mastered_status) return false; // 已掌握的不算薄弱
      
      // 统计该字词的复习次数
      const wordPlans = allPlans.filter(plan => 
        plan.word_id === word.word_id && plan.completed
      );
      
      return wordPlans.length >= 3; // 复习3次以上仍未掌握
    });

    return weakWords;
  }
}

module.exports = EbbinghausManager;
